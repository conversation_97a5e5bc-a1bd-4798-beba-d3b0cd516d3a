"""
Dubizzle Egypt price estimator.
"""

import logging
import time
from typing import Union
import urllib.parse

from selenium.webdriver.common.by import By

from .base import BasePriceEstimator
from ..utils.web_scraping import WebScrapingUtils
from ..utils.text_processing import TextProcessor
from ..config.estimation_config import EstimationConfig

logger = logging.getLogger('price_estimation')


class DubizzlePriceEstimator(BasePriceEstimator):
    """
    Dubizzle Egypt price estimator using Selenium (like Amazon approach).

    This estimator now uses browser automation for better reliability:
    - Handles dynamic content loading
    - Bypasses anti-bot measures
    - Works with current website structure
    - More reliable than requests-based approach
    """

    def __init__(self):
        super().__init__()
        self.text_processor = TextProcessor()

    def estimate_price(self, name: str, description: str) -> Union[float, str]:
        """Estimate price from Dubizzle Egypt using Selenium."""
        # Create search query
        search_query = f"{name} {description}".strip()
        search_query_encoded = urllib.parse.quote(search_query)

        # Use the current Dubizzle search URL format
        url = f"https://www.dubizzle.com.eg/search?q={search_query_encoded}"

        logger.debug(f"Dubizzle URL: {url}")

        driver = None
        try:
            driver = WebScrapingUtils.get_webdriver()
            driver.get(url)
            time.sleep(3)  # Wait for page to load

            # Look for price elements using multiple selectors
            prices = self._extract_prices_from_page(driver, name, description)

            if prices:
                average_price = sum(prices) / len(prices)
                logger.info(f"Found {len(prices)} matching prices on Dubizzle")

                # Apply discount for used items
                if self.text_processor.is_used_item(description) or self.text_processor.is_used_item(name):
                    average_price *= EstimationConfig.USED_DISCOUNT_FACTOR

                return round(average_price, 2)

            return "No price found on Dubizzle"

        except Exception as e:
            logger.error(f"Dubizzle scraping error: {str(e)}")
            return f"Dubizzle estimation failed: {str(e)}"
        finally:
            if driver:
                driver.quit()

    def _extract_prices_from_page(self, driver, name: str, description: str) -> list:
        """Extract prices from Dubizzle search results page."""
        prices = []

        try:
            # Wait a bit more for dynamic content to load
            time.sleep(2)

            # Try to find listing containers using multiple selectors
            listing_selectors = [
                "div[data-testid='listing-card']",
                "article",
                "div.listing",
                "div.item",
                ".listing-item"
            ]

            listings = []
            for selector in listing_selectors:
                try:
                    elements = driver.find_elements(By.CSS_SELECTOR, selector)
                    if elements:
                        listings = elements[:20]  # Limit to first 20 listings
                        logger.debug(f"Found {len(listings)} listings using selector: {selector}")
                        break
                except Exception as e:
                    logger.debug(f"Selector {selector} failed: {str(e)}")
                    continue

            if not listings:
                logger.warning("No listings found on Dubizzle page")
                return prices

            # Extract prices from each listing
            for i, listing in enumerate(listings):
                try:
                    price = self._extract_price_from_listing(listing, name, description)
                    if price:
                        prices.append(price)
                        logger.debug(f"Extracted price {price} from listing {i+1}")
                except Exception as e:
                    logger.debug(f"Failed to extract price from listing {i+1}: {str(e)}")
                    continue

            logger.debug(f"Total prices extracted: {len(prices)}")
            return prices

        except Exception as e:
            logger.error(f"Error extracting prices from Dubizzle page: {str(e)}")
            return prices

    def _extract_price_from_listing(self, listing, name: str, description: str) -> float:
        """Extract price from a single Dubizzle listing."""
        try:
            # Try to find title element
            title_selectors = [
                "h2", "h3", "h4",
                "a[data-testid='listing-title']",
                ".title",
                "a.title",
                "div.title"
            ]

            title_text = ""
            for selector in title_selectors:
                try:
                    title_element = listing.find_element(By.CSS_SELECTOR, selector)
                    title_text = title_element.text.strip()
                    if title_text:
                        break
                except:
                    continue

            if not title_text:
                return None

            logger.debug(f"Found listing title: {title_text[:50]}...")

            # Calculate similarity
            search_text = f"{name} {description}".lower()
            similarity = self.text_processor.calculate_similarity(title_text.lower(), search_text)

            logger.debug(f"Similarity score: {round(similarity, 2)} (threshold: {EstimationConfig.SIMILARITY_THRESHOLD})")

            if similarity < EstimationConfig.SIMILARITY_THRESHOLD:
                return None

            # Try to find price element
            price_selectors = [
                "span[class*='price']",
                "div[class*='price']",
                "span[data-testid='listing-price']",
                ".price",
                "span:contains('ج.م')",
                "div:contains('ج.م')",
                "span:contains('EGP')",
                "div:contains('EGP')"
            ]

            price_text = ""
            for selector in price_selectors:
                try:
                    price_element = listing.find_element(By.CSS_SELECTOR, selector)
                    price_text = price_element.text.strip()
                    if price_text and any(char.isdigit() for char in price_text):
                        break
                except:
                    continue

            if not price_text:
                return None

            logger.debug(f"Found price text: {price_text}")

            # Extract price using text processor
            price = self.text_processor.extract_price_from_text(price_text)
            if price:
                logger.debug(f"Extracted price: {price}")
                return price

            return None

        except Exception as e:
            logger.debug(f"Error extracting price from listing: {str(e)}")
            return None
